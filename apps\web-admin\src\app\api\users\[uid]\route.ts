/**
 * API Route: Get User by UID
 * 
 * This route fetches user data from the users collection
 * to support admin authentication with the existing schema.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAdminFirestore } from '@/lib/firebase-admin';

export async function GET(
  request: NextRequest,
  { params }: { params: { uid: string } }
) {
  try {
    const { uid } = params;

    if (!uid) {
      return NextResponse.json(
        { error: 'User UID is required' },
        { status: 400 }
      );
    }

    // Get Firestore instance
    const db = getAdminFirestore();

    // Get user document
    const userDoc = await db.collection('users').doc(uid).get();

    if (!userDoc.exists) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const userData = userDoc.data();

    // Return user data
    return NextResponse.json({
      id: userDoc.id,
      email: userData?.email,
      displayName: userData?.displayName,
      role: userData?.role,
      permissions: userData?.permissions,
      isActive: userData?.isActive,
      firstName: userData?.firstName,
      lastName: userData?.lastName,
      department: userData?.department,
      title: userData?.title,
      createdAt: userData?.createdAt?.toDate?.()?.toISOString(),
      updatedAt: userData?.updatedAt?.toDate?.()?.toISOString(),
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

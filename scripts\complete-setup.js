/**
 * Complete Setup Script
 * 
 * This script performs the complete setup including:
 * 1. Initialize admin system (if not already done)
 * 2. Ensure users collection is ready
 * 3. Provide instructions for creating John admin user
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function completeSetup() {
  try {
    console.log('🚀 Starting complete setup...');
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();

    // Step 1: Check if admin system is initialized
    console.log('\n1️⃣  Checking admin system...');
    const adminSystemDoc = await db.collection('system_config').doc('admin_system').get();
    
    if (!adminSystemDoc.exists) {
      console.log('   ⚠️  Admin system not initialized');
      console.log('   📝 You need to initialize the admin system first');
      console.log('   🚀 Run: firebase deploy --only functions');
      console.log('   📞 Then call the initializeAdminSystem function');
    } else {
      console.log('   ✅ Admin system is initialized');
      const adminConfig = adminSystemDoc.data();
      console.log(`   📅 Initialized at: ${adminConfig.initializedAt.toDate().toLocaleDateString()}`);
    }

    // Step 2: Check users collection
    console.log('\n2️⃣  Checking users collection...');
    const usersSnapshot = await db.collection('users').limit(1).get();
    if (usersSnapshot.empty) {
      console.log('   ❌ Users collection is empty');
    } else {
      console.log('   ✅ Users collection exists and is populated');
    }

    // Step 3: Check user preferences collection
    console.log('\n3️⃣  Checking user preferences collection...');
    const preferencesSnapshot = await db.collection('user_preferences').limit(1).get();
    if (preferencesSnapshot.empty) {
      console.log('   ❌ User preferences collection is empty');
    } else {
      console.log('   ✅ User preferences collection exists and is populated');
    }

    // Step 4: Summary and next steps
    console.log('\n📊 SETUP STATUS SUMMARY\n');
    
    console.log('✅ Users Collection Setup:');
    console.log('   - users collection created and populated');
    console.log('   - user_preferences collection created and populated');
    console.log('   - System configuration in place');
    console.log('   - Sample data available for testing');

    console.log('\n🔧 Cloud Functions Setup:');
    console.log('   - User management functions created');
    console.log('   - User authentication triggers created');
    console.log('   - Admin user creation function ready');

    console.log('\n📋 NEXT STEPS TO COMPLETE SETUP:\n');

    console.log('1️⃣  Deploy Cloud Functions:');
    console.log('   firebase deploy --only functions');

    console.log('\n2️⃣  Initialize Admin System (if not done):');
    console.log('   - Go to Firebase Console Functions');
    console.log('   - Call initializeAdminSystem function');
    console.log('   - Provide super admin details when prompted');

    console.log('\n3️⃣  Create John Admin User:');
    console.log('   - After admin system is initialized');
    console.log('   - Call createJohnAdmin function');
    console.log('   - This <NAME_EMAIL> with specified password');

    console.log('\n4️⃣  Verify Setup:');
    console.log('   - Check Firestore console for collections');
    console.log('   - Test admin login');
    console.log('   - Verify user management functions');

    console.log('\n🔗 USEFUL LINKS:\n');
    console.log(`📊 Firestore Console: https://console.firebase.google.com/project/${serviceAccount.projectId}/firestore/data`);
    console.log(`⚡ Functions Console: https://console.firebase.google.com/project/${serviceAccount.projectId}/functions/list`);
    console.log(`🔐 Authentication Console: https://console.firebase.google.com/project/${serviceAccount.projectId}/authentication/users`);

    console.log('\n📁 COLLECTIONS CREATED:\n');
    console.log('✅ users - Main user profiles');
    console.log('✅ user_preferences - Detailed user preferences');
    console.log('✅ system_config - System configuration');
    console.log('📋 admin_users - Admin user profiles (after admin init)');
    console.log('📋 admin_roles - Admin roles (after admin init)');
    console.log('📋 admin_permissions - Admin permissions (after admin init)');

    console.log('\n🎯 ADMIN USER DETAILS:\n');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: @Iamachessgrandmaster23');
    console.log('👑 Role: Administrator');
    console.log('🔐 Permissions: Content management, user management, analytics view');

    console.log('\n✅ Setup preparation completed successfully!');
    console.log('🚀 Ready to deploy and initialize the system.');

  } catch (error) {
    console.error('❌ Error during setup:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  completeSetup()
    .then(() => {
      console.log('\n🏁 Complete setup script finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Setup script failed:', error);
      process.exit(1);
    });
}

module.exports = { completeSetup };

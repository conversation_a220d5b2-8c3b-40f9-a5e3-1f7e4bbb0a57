'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Shield } from 'lucide-react';
import { useCustomAdminAuth } from '@/hooks/useCustomAdminAuth';

export default function HomePage() {
  const router = useRouter();
  const { user, loading, isAdmin, sessionValid } = useCustomAdminAuth();

  useEffect(() => {
    // Only redirect to login if not authenticated
    if (!loading && (!user || !isAdmin || !sessionValid)) {
      router.push('/login');
    }
  }, [user, loading, isAdmin, sessionValid, router]);
  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Shield className="w-6 h-6 text-white animate-pulse" />
          </div>
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading admin panel...</p>
          <p className="text-gray-500 text-sm mt-1">Please wait while we secure your session</p>
        </div>
      </div>
    );
  }

  // Show admin dashboard if authenticated
  if (user && isAdmin && sessionValid) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <Shield className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
                  <p className="text-sm text-gray-500">Welcome back, {user.displayName || user.email}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Admin
                </span>
                <button
                  onClick={() => {
                    // Add sign out functionality here if needed
                    console.log('Sign out clicked');
                  }}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Sign Out
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          <div className="px-4 py-6 sm:px-0">
            <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
              <div className="text-center">
                <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Admin Panel Ready</h3>
                <p className="text-gray-500">Your admin dashboard is successfully loaded.</p>
                <p className="text-sm text-gray-400 mt-2">
                  User: {user.email} | Role: Admin | UID: {user.uid}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show redirecting message (this should not be visible due to useEffect redirect)
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center mx-auto mb-4">
          <Shield className="w-6 h-6 text-white animate-pulse" />
        </div>
        <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-gray-600 font-medium">Redirecting to admin login...</p>
        <p className="text-gray-500 text-sm mt-1">Please wait while we secure your session</p>
      </div>
    </div>
  );
}

/**
 * Custom Admin Authentication Hook
 * 
 * This hook works with the existing users and roles collections
 * instead of the admin_users collection.
 */

'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@encreasl/auth';
import { User as FirebaseUser } from 'firebase/auth';

interface AdminUserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  createdAt: Date;
  lastLoginAt: Date | null;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  role: string;
  permissions: string[];
  department?: string;
  title?: string;
  mfaEnabled: boolean;
  sessionTimeout: number;
}

interface UseCustomAdminAuthReturn {
  user: FirebaseUser | null;
  loading: boolean;
  error: any;
  signIn: (credentials: { email: string; password: string }) => Promise<any>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  profile: AdminUserProfile | null;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  hasPermission: (permission: string) => boolean;
  sessionValid: boolean;
}

export function useCustomAdminAuth(): UseCustomAdminAuthReturn {
  const baseAuth = useAuth();
  const [profile, setProfile] = useState<AdminUserProfile | null>(null);
  const [sessionValid, setSessionValid] = useState(true);

  // Load admin profile when user changes
  useEffect(() => {
    if (baseAuth.user) {
      loadAdminProfile(baseAuth.user);
    } else {
      setProfile(null);
    }
  }, [baseAuth.user]);

  const loadAdminProfile = async (user: FirebaseUser) => {
    try {
      console.log('🔍 Loading admin profile for user:', user.uid, user.email);

      // Get custom claims first
      const idTokenResult = await user.getIdTokenResult();
      const claims = idTokenResult.claims;
      console.log('🏷️ Custom claims:', claims);

      // Check if user has admin role in Firestore users collection
      let userData = null;
      let roleData = null;

      try {
        console.log('📡 Fetching user data from API...');
        const userResponse = await fetch(`/api/users/${user.uid}`);
        console.log('📡 User API response status:', userResponse.status);

        if (userResponse.ok) {
          userData = await userResponse.json();
          console.log('👤 User data:', userData);

          // If user has admin role, fetch role details
          if (userData.role === 'admin') {
            console.log('📡 Fetching role data from API...');
            const roleResponse = await fetch(`/api/roles/${userData.role}`);
            console.log('📡 Role API response status:', roleResponse.status);

            if (roleResponse.ok) {
              roleData = await roleResponse.json();
              console.log('👑 Role data:', roleData);
            } else {
              console.warn('⚠️ Failed to fetch role data:', await roleResponse.text());
            }
          }
        } else {
          console.warn('⚠️ Failed to fetch user data:', await userResponse.text());
        }
      } catch (fetchError) {
        console.error('❌ Error fetching user/role data from API:', fetchError);
      }

      // Determine admin status from Firestore data or custom claims
      const isAdmin = userData?.role === 'admin' || claims.admin === true;
      const permissions = roleData?.permissions || userData?.permissions || claims.permissions || [];

      // Create admin profile
      const adminProfile: AdminUserProfile = {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
        createdAt: new Date(user.metadata.creationTime || Date.now()),
        lastLoginAt: new Date(user.metadata.lastSignInTime || Date.now()),
        isAdmin: isAdmin,
        isSuperAdmin: claims.superAdmin === true || (userData?.role === 'admin' && roleData?.level >= 100),
        role: userData?.role || claims.role || 'viewer',
        permissions: permissions,
        department: userData?.department || claims.department,
        title: userData?.title || claims.title,
        mfaEnabled: false, // TODO: Implement MFA check
        sessionTimeout: claims.sessionTimeout || 3600,
      };

      setProfile(adminProfile);
    } catch (error) {
      console.error('Error loading admin profile:', error);
      setProfile(null);
    }
  };

  const hasPermission = useCallback((permission: string): boolean => {
    if (!profile) return false;
    if (profile.isSuperAdmin) return true;
    return profile.permissions.includes(permission) || profile.permissions.includes('*');
  }, [profile]);

  const updateLastActivity = () => {
    localStorage.setItem('last_activity', Date.now().toString());
  };

  // Update last activity on user interaction
  useEffect(() => {
    const updateActivity = () => updateLastActivity();
    
    window.addEventListener('click', updateActivity);
    window.addEventListener('keypress', updateActivity);
    window.addEventListener('scroll', updateActivity);

    return () => {
      window.removeEventListener('click', updateActivity);
      window.removeEventListener('keypress', updateActivity);
      window.removeEventListener('scroll', updateActivity);
    };
  }, []);

  return {
    ...baseAuth,
    profile,
    isAdmin: profile?.isAdmin || false,
    isSuperAdmin: profile?.isSuperAdmin || false,
    hasPermission,
    sessionValid,
  };
}

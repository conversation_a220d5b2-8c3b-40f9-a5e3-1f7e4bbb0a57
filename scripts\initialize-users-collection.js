/**
 * Initialize Users Collection Script
 * 
 * This script creates the users collection structure and adds sample data
 * to make it visible in Firestore console.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function initializeUsersCollection() {
  try {
    console.log('🚀 Starting users collection initialization...');
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    // Step 1: Create a sample user document to initialize the collection
    console.log('📝 Creating sample user document...');
    
    const sampleUserId = 'sample-user-' + Date.now();
    const sampleUser = {
      id: sampleUserId,
      email: '<EMAIL>',
      displayName: 'Sample User',
      authProvider: 'email',
      emailVerified: false,
      phoneVerified: false,
      
      // Profile
      firstName: 'Sample',
      lastName: 'User',
      bio: 'This is a sample user created to initialize the users collection.',
      
      // Status & permissions
      isActive: true,
      isVerified: false,
      accountType: 'free',
      
      // Activity tracking
      lastLoginAt: null,
      lastActiveAt: null,
      loginCount: 0,
      failedLoginAttempts: 0,
      
      // Preferences (denormalized)
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        currency: 'USD',
        notifications: {
          email: true,
          push: true,
          sms: false,
          marketing: false,
        },
        privacy: {
          profileVisibility: 'public',
          showEmail: false,
          showPhone: false,
          allowSearchByEmail: true,
        },
      },
      
      // Analytics
      analytics: {
        signupSource: 'script',
        totalSessions: 0,
        totalTimeSpent: 0,
        firstVisitAt: admin.firestore.Timestamp.now(),
      },
      
      // Compliance
      compliance: {
        gdprCompliant: true,
        ccpaCompliant: true,
        termsAcceptedAt: admin.firestore.Timestamp.now(),
        privacyPolicyAcceptedAt: admin.firestore.Timestamp.now(),
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system',
      updatedBy: 'system',
      version: 1,
      
      // Soft delete
      isDeleted: false,
    };

    await db.collection('users').doc(sampleUserId).set(sampleUser);
    console.log('✅ Created sample user document');

    // Step 2: Create corresponding user preferences document
    console.log('⚙️  Creating sample user preferences...');
    
    const samplePreferences = {
      userId: sampleUserId,
      
      // UI preferences
      theme: 'light',
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      currency: 'USD',
      
      // Notification preferences
      notifications: {
        email: true,
        push: true,
        sms: false,
        marketing: false,
        productUpdates: true,
        securityAlerts: true,
        weeklyDigest: false,
        monthlyReport: false,
      },
      
      // Privacy preferences
      privacy: {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
        allowSearchByEmail: true,
        allowDataCollection: true,
        allowPersonalization: true,
      },
      
      // Communication preferences
      communication: {
        preferredContactMethod: 'email',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
          timezone: 'UTC',
        },
        frequency: {
          marketing: 'weekly',
          updates: 'immediate',
        },
      },
      
      // Metadata
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      version: 1,
    };

    await db.collection('user_preferences').doc(sampleUserId).set(samplePreferences);
    console.log('✅ Created sample user preferences');

    // Step 3: Create a few more sample users for demonstration
    console.log('👥 Creating additional sample users...');
    
    const additionalUsers = [
      {
        email: '<EMAIL>',
        displayName: 'Demo User One',
        firstName: 'Demo',
        lastName: 'User One',
        accountType: 'premium',
        signupSource: 'web',
      },
      {
        email: '<EMAIL>',
        displayName: 'Demo User Two',
        firstName: 'Demo',
        lastName: 'User Two',
        accountType: 'free',
        signupSource: 'mobile',
      },
      {
        email: '<EMAIL>',
        displayName: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        accountType: 'enterprise',
        signupSource: 'referral',
      },
    ];

    for (let i = 0; i < additionalUsers.length; i++) {
      const userData = additionalUsers[i];
      const userId = `demo-user-${i + 1}-${Date.now()}`;
      
      const user = {
        ...sampleUser,
        id: userId,
        email: userData.email,
        displayName: userData.displayName,
        firstName: userData.firstName,
        lastName: userData.lastName,
        accountType: userData.accountType,
        analytics: {
          ...sampleUser.analytics,
          signupSource: userData.signupSource,
        },
      };

      await db.collection('users').doc(userId).set(user);
      
      const preferences = {
        ...samplePreferences,
        userId: userId,
      };
      
      await db.collection('user_preferences').doc(userId).set(preferences);
      
      console.log(`✅ Created demo user: ${userData.displayName}`);
    }

    // Step 4: Create collection metadata document
    console.log('📊 Creating collection metadata...');
    
    await db.collection('system_config').doc('users_collection').set({
      initialized: true,
      initializedAt: admin.firestore.Timestamp.now(),
      version: '1.0.0',
      totalUsers: 4, // Sample + 3 demo users
      features: {
        userProfiles: true,
        preferences: true,
        analytics: true,
        compliance: true,
        softDelete: true,
      },
      schema: {
        version: '1.0.0',
        lastUpdated: admin.firestore.Timestamp.now(),
      },
    });

    console.log('✅ Created collection metadata');

    console.log('\n🎉 Users collection initialized successfully!');
    console.log('📊 Collections created:');
    console.log('  - users (4 sample documents)');
    console.log('  - user_preferences (4 sample documents)');
    console.log('  - system_config/users_collection (metadata)');
    console.log('\n✅ You can now see the users collection in Firestore console.');

  } catch (error) {
    console.error('❌ Error initializing users collection:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  initializeUsersCollection()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { initializeUsersCollection };

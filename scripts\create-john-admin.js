/**
 * Create John Admin User Script
 * 
 * This script creates the specific admin user requested:
 * Email: <EMAIL>
 * Password: @Iamachessgrandmaster23
 * Role: Admin
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

// Admin user details
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = '@Iamachessgrandmaster23';
const ADMIN_NAME = 'John Admin';
const ADMIN_ROLE = 'admin'; // Using 'admin' role instead of 'super-admin'

async function createJohnAdmin() {
  try {
    console.log('🚀 Starting John admin user creation...');
    console.log('📊 Project ID:', serviceAccount.projectId);
    console.log('👤 Email:', ADMIN_EMAIL);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const auth = admin.auth();
    const db = admin.firestore();

    // Step 1: Create user in Firebase Auth
    console.log('🔧 Creating admin user in Firebase Auth...');
    
    let userRecord;
    try {
      // Try to create the user
      userRecord = await auth.createUser({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
        displayName: ADMIN_NAME,
        emailVerified: true,
      });
      
      console.log('✅ Created Firebase Auth user:', ADMIN_EMAIL);
    } catch (error) {
      if (error.code === 'auth/email-already-exists') {
        // User already exists, get their record
        userRecord = await auth.getUserByEmail(ADMIN_EMAIL);
        console.log('ℹ️  Firebase Auth user already exists:', ADMIN_EMAIL);
        
        // Update password if needed
        await auth.updateUser(userRecord.uid, {
          password: ADMIN_PASSWORD,
          displayName: ADMIN_NAME,
          emailVerified: true,
        });
        console.log('✅ Updated existing user password and details');
      } else {
        throw error;
      }
    }

    // Step 2: Check if admin role exists
    console.log('🔍 Checking admin role...');
    const adminRoleDoc = await db.collection('admin_roles').doc(ADMIN_ROLE).get();
    
    if (!adminRoleDoc.exists) {
      console.error('❌ Admin role not found. Please run the admin system initialization first.');
      process.exit(1);
    }

    const roleData = adminRoleDoc.data();
    console.log('✅ Found admin role:', roleData.displayName);

    // Step 3: Create admin user document in Firestore
    console.log('📝 Creating admin user document in Firestore...');
    
    const adminUserDoc = {
      id: userRecord.uid,
      email: ADMIN_EMAIL,
      displayName: ADMIN_NAME,
      authProvider: 'email',
      emailVerified: true,
      
      // Role information (denormalized for performance)
      roleRef: `admin_roles/${ADMIN_ROLE}`,
      roleName: roleData.displayName,
      permissions: roleData.permissions,
      isActive: true,
      isSuperAdmin: false, // This is an admin, not super admin
      
      // Profile information
      firstName: ADMIN_NAME.split(' ')[0],
      lastName: ADMIN_NAME.split(' ').slice(1).join(' '),
      title: 'Administrator',
      department: 'Administration',
      
      // CMS capabilities based on admin role
      cmsPermissions: {
        content: {
          canCreate: true,
          canEdit: true,
          canDelete: false, // Admins can't delete content
          canPublish: true,
          canSchedule: true,
        },
        media: {
          canUpload: true,
          canDelete: true,
          canOrganize: true,
          maxUploadSize: 50, // 50MB for admins
        },
        users: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: false, // Admins can't delete users
          canChangeRoles: false, // Only super admins can change roles
        },
        analytics: {
          canView: true,
          canExport: false, // Only super admins can export
          canConfigureDashboards: false,
        },
        settings: {
          canViewSystem: true,
          canEditSystem: false, // Only super admins can edit system settings
          canManageIntegrations: false,
          canManageBackups: false,
        },
      },
      
      // Workflow permissions
      workflowPermissions: {
        canApproveContent: true,
        canRejectContent: true,
        canAssignTasks: true,
        canCreateWorkflows: false, // Only super admins
        approvalLevel: 3, // Admin level
      },
      
      // Default access restrictions
      accessRestrictions: {
        requireMFA: false,
        sessionTimeout: 60, // 1 hour
        maxConcurrentSessions: 3,
      },
      
      // Activity tracking
      loginCount: 0,
      failedLoginAttempts: 0,
      
      // Default preferences
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true,
      },
      
      // Timestamps
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      createdBy: 'system',
      updatedBy: 'system',
      version: 1,
      
      // Soft delete
      isDeleted: false,
    };

    // Check if admin user document already exists
    const existingAdminDoc = await db.collection('admin_users').doc(userRecord.uid).get();
    
    if (existingAdminDoc.exists) {
      console.log('ℹ️  Admin user document already exists, updating...');
      await db.collection('admin_users').doc(userRecord.uid).update({
        ...adminUserDoc,
        updatedAt: admin.firestore.Timestamp.now(),
      });
    } else {
      await db.collection('admin_users').doc(userRecord.uid).set(adminUserDoc);
      console.log('✅ Created admin user document');
    }

    // Step 4: Set custom claims for Firebase Auth
    console.log('🔐 Setting custom claims...');
    await auth.setCustomUserClaims(userRecord.uid, {
      admin: true,
      superAdmin: false,
      role: ADMIN_ROLE,
      permissions: roleData.permissions,
    });
    console.log('✅ Set custom claims');

    // Step 5: Update role user count
    console.log('📊 Updating role user count...');
    await db.collection('admin_roles').doc(ADMIN_ROLE).update({
      userCount: admin.firestore.FieldValue.increment(1),
      lastUsedAt: admin.firestore.Timestamp.now(),
    });

    // Step 6: Create admin preferences document
    console.log('⚙️  Creating admin preferences...');
    const preferencesDoc = await db.collection('admin_preferences').doc(userRecord.uid).get();
    
    if (!preferencesDoc.exists) {
      await db.collection('admin_preferences').doc(userRecord.uid).set({
        userId: userRecord.uid,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true,
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
      });
      console.log('✅ Created admin preferences');
    } else {
      console.log('ℹ️  Admin preferences already exist');
    }

    console.log('\n🎉 John admin user created successfully!');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('🔑 Password:', ADMIN_PASSWORD);
    console.log('👤 Role:', roleData.displayName);
    console.log('🆔 UID:', userRecord.uid);
    console.log('\n✅ The admin user can now log in to the admin panel.');

  } catch (error) {
    console.error('❌ Error creating John admin user:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createJohnAdmin()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createJohnAdmin };
